import {
  Home,
  Utensils,
  Car,
  Zap,
  Coffee,
  Heart,
  HelpCircle,
  Briefcase,
  ShoppingBag,
  ShoppingCart,
  Lightbulb,
  Film,
  Settings,
  Plane,
  Smartphone,
  CircleDollarSign,
  MoreHorizontal,
  type LucideIcon,
} from "lucide-react";
import { cache } from "react";
import { prisma } from "@/lib/db";

// Type for database-stored categories
type DbCategory = {
  id: string;
  name: string;
  icon: string;
  color: string;
  keywords: string[];
  type: "CREDIT" | "DEBIT";
};

// Type for our configuration format
export interface CategoryConfig {
  id: string;
  name: string;
  icon: string;
  color: string;
  keywords: string[];
  type: "CREDIT" | "DEBIT";
}

// Function to convert icon component to string name for storage
export function getIconNameFromComponent(IconComponent: LucideIcon): string {
  // You need to map each icon component to its string name
  const iconMap: Record<string, string> = {
    [Briefcase.name]: 'briefcase',
    [Home.name]: 'home',
    [Utensils.name]: 'utensils',
    [Car.name]: 'car',
    [Zap.name]: 'zap',
    [Coffee.name]: 'coffee',
    [Heart.name]: 'heart',
    [ShoppingBag.name]: 'shopping-bag',
    [Plane.name]: 'plane',
    [Smartphone.name]: 'smartphone',
    [CircleDollarSign.name]: 'circle-dollar-sign',
    [HelpCircle.name]: 'help-circle',
  };
  
  return iconMap[IconComponent.name] || 'help-circle'; // fallback to help-circle
}

// Static fallback categories
export const CATEGORIES: CategoryConfig[] = [
  {
    id: "income",
    name: "INCOME",
    icon: "Briefcase",
    color: "hsl(var(--chart-0))",
    keywords: ["salary", "deposit", "payroll", "income"],
    type: "CREDIT",
  },
  {
    id: "expense",
    name: "EXPENSE",
    icon: "Briefcase",
    color: "hsl(var(--chart-0))",
    keywords: ["withdrawl", "pay", "expense"],
    type: "DEBIT",
  },
  {
    id: "housing",
    name: "HOUSING",
    icon: "home",
    color: "hsl(var(--chart-1))",
    keywords: ["rent", "mortgage", "house", "apartment", "housing"],
    type: "DEBIT",
  },
  {
    id: "food",
    name: "FOOD",
    icon: "utensils",
    color: "hsl(var(--chart-2))",
    keywords: [
      "grocery",
      "restaurant",
      "food",
      "meal",
      "dining",
      "cafe",
      "coffee",
    ],
    type: "DEBIT",
  },
  {
    id: "transportation",
    name: "TRANSPORTATION",
    icon: 'Car',
    color: "hsl(var(--chart-3))",
    keywords: [
      "gas",
      "fuel",
      "car",
      "bus",
      "train",
      "transport",
      "uber",
      "lyft",
      "parking",
    ],
    type: "DEBIT",
  },
  {
    id: "utilities",
    name: "UTILITIES",
    icon: 'Zap',
    color: "hsl(var(--chart-4))",
    keywords: [
      "electric",
      "water",
      "gas",
      "internet",
      "phone",
      "utility",
      "bill",
    ],
    type: "DEBIT",
  },
  {
    id: "entertainment",
    name: "ENTERTAINMENT",
    icon: 'Coffee',
    color: "hsl(var(--chart-5))",
    keywords: [
      "movie",
      "game",
      "entertainment",
      "fun",
      "hobby",
      "netflix",
      "spotify",
    ],
    type: "DEBIT",
  },
  {
    id: "healthcare",
    name: "HEALTHECARE",
    icon: 'Heart',
    color: "hsl(var(--chart-6))",
    keywords: ["doctor", "medical", "health", "medicine", "pharmacy", "dental"],
    type: "DEBIT",
  },
  {
    id: "shopping",
    name: "SHOPPING",
    icon: 'ShoppingBag',
    color: "hsl(var(--chart-7))",
    keywords: ["amazon", "walmart", "target", "clothing", "electronics"],
    type: "DEBIT",
  },
  {
    id: "travel",
    name: "TRAVEL",
    icon: 'Plane',
    color: "hsl(var(--chart-8))",
    keywords: ["flight", "hotel", "airbnb", "vacation", "travel"],
    type: "DEBIT",
  },
  {
    id: "subscription",
    name: "SUBSCRIPTION",
    icon: 'Smartphone',
    color: "hsl(var(--chart-9))",
    keywords: ["subscription", "membership", "recurring"],
    type: "DEBIT",
  },
  {
    id: "timeDeposit",
    name: "TIMEDEPOSIT",
    icon: 'CircleDollarSign',
    color: "hsl(var(--chart-10))",
    keywords: ["fixed deposit", "term deposit", "certificate of deposit", "cd"],
    type: "DEBIT",
  },
  {
    id: "other",
    name: "OTHER",
    icon: 'HelpCircle',
    color: "hsl(var(--chart-10))",
    keywords: [],
    type: "DEBIT",
  },
];


// Cached function to get categories from database
export const getCategories = cache(
  async (userId: string): Promise<CategoryConfig[]> => {
    try {
      const dbCategories = await prisma.category.findMany({
        where: { userId },
        select: {
          id: true,
          name: true,
          icon: true,
          color: true,
          keywords: true,
          type: true,
        }
      });

      if (dbCategories.length === 0) {
        // If no categories in DB, create default ones and return them
        const defaultCategories = await Promise.all(
          CATEGORIES.map(async (cat) => {
            return prisma.category.create({
              data: {
                name: cat.name,
                icon: cat.icon,
                color: cat.color,
                keywords: cat.keywords,
                type: cat.type,
                userId,
              },
              select: {
                id: true,
                name: true,
                icon: true,
                color: true,
                keywords: true,
                type: true,
              }
            });
          })
        );

        // Convert DB categories to CategoryConfig format
        return defaultCategories.map((dbCat: DbCategory): CategoryConfig => ({
          id: dbCat.id,
          name: dbCat.name,
          icon: dbCat.icon,
          color: dbCat.color,
          keywords: dbCat.keywords,
          type: dbCat.type,
        }));
      }

      // Convert existing DB categories to CategoryConfig format
      return dbCategories.map((dbCat: DbCategory): CategoryConfig => ({
        id: dbCat.id,
        name: dbCat.name,
        icon: dbCat.icon,
        color: dbCat.color,
        keywords: dbCat.keywords,
        type: dbCat.type,
      }));
    } catch (error) {
      console.error("Error fetching categories:", error);
      return CATEGORIES; // Fallback to static categories on error
    }
  }
);

export async function findCategoryById(
  id: string,
  userId: string
): Promise<CategoryConfig | undefined> {
  const categories = await getCategories(userId);
  return categories.find((cat) => cat.id === id);
}

export async function suggestCategoryForTransaction(
  description: string,
  type: "CREDIT" | "DEBIT",
  userId: string
): Promise<string> {
  const categories = await getCategories(userId);

  if (type === "CREDIT") {
    const incomeCategory = categories.find((cat) => cat.type === "CREDIT");
    if (incomeCategory) {
      return incomeCategory.id;
    }
  }

  const lowerDesc = description.toLowerCase();

  for (const category of categories) {
    if (category.type === "CREDIT" && type === "DEBIT") continue;

    if (category.keywords.some((keyword) => lowerDesc.includes(keyword))) {
      return category.id;
    }
  }

  const otherCategory = categories.find(
    (cat) => cat.name.toLowerCase() === "other"
  );
  return otherCategory?.id || "other";
}

// Function to get icon component from string name
export function getIconByName(iconName: string): LucideIcon {
  const iconMap: Record<string, LucideIcon> = {
    'Briefcase': Briefcase,
    'Home': Home,
    'Utensils': Utensils,
    'Car': Car,
    'Zap': Zap,
    'Coffee': Coffee,
    'Heart': Heart,
    'ShoppingBag': ShoppingBag,
    'Plane': Plane,
    'Smartphone': Smartphone,
    'CircleDollarSign': CircleDollarSign,
    'HelpCircle': HelpCircle,
    'Film': Film,
    'ShoppingCart': ShoppingCart,
    'Lightbulb': Lightbulb,
    'MoreHorizontal': MoreHorizontal,
    'Idea': Lightbulb,
    'Settings': Settings,
  };
    return iconMap[iconName] || HelpCircle;
}

export const CATEGORY_ICONS: Record<string, LucideIcon> = {
  Home: Home,
  Utensils: Utensils,
  Briefcase: Briefcase,
  Car: Car,
  Film: Film,
  Idea: Lightbulb,
  Settings: Settings,
  Plane: Plane,
  Heart: Heart,
  Coffee: Coffee,
  Zap: Zap,
  ShoppingCart: ShoppingCart,
  ShoppingBag: ShoppingBag,
  Smartphone: Smartphone,
  CircleDollarSign: CircleDollarSign,
  Lightbulb: Lightbulb,
  HelpCircle: HelpCircle,
  MoreHorizontal: MoreHorizontal,
};

export const CATEGORY_COLORS = [
  { name: "Dark Blue", value: "hsl(var(--chart-0))", bg: "hsl(215 50% 23%)" },
  { name: "Orange Red", value: "hsl(var(--chart-1))", bg: "hsl(12 76% 61%)" },
  { name: "Teal", value: "hsl(var(--chart-2))", bg: "hsl(173 58% 39%)" },
  { name: "Dark Slate", value: "hsl(var(--chart-3))", bg: "hsl(197 37% 24%)" },
  { name: "Yellow", value: "hsl(var(--chart-4))", bg: "hsl(43 74% 66%)" },
  { name: "Orange", value: "hsl(var(--chart-5))", bg: "hsl(27 87% 67%)" },
  { name: "Purple", value: "hsl(var(--chart-6))", bg: "hsl(300 65% 55%)" },
  { name: "Green", value: "hsl(var(--chart-7))", bg: "hsl(152 45% 50%)" },
  { name: "Cyan", value: "hsl(var(--chart-8))", bg: "hsl(190 95% 39%)" },
  { name: "Pink", value: "hsl(var(--chart-9))", bg: "hsl(350 60% 55%)" },
  { name: "Blue Gray", value: "hsl(var(--chart-10))", bg: "hsl(240 20% 60%)" },
  { name: "Blue", value: "hsl(var(--chart-11))", bg: "hsl(220 80% 60%)" },
];

// Helper function to get the icon component
export function getIconComponent(iconName: string): React.ComponentType<any> {
  return CATEGORY_ICONS[iconName] || MoreHorizontal;
}
